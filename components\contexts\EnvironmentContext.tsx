"use client"

import React, { createContext, useContext, useState } from 'react';
import { useEnvironmentPersistence } from '@/hooks/useEnvironmentPersistence';
import type { EnvironmentState, Environment } from '@/types/env';

interface EnvironmentContextType {
  environmentState: EnvironmentState;
  setEnvironmentState: React.Dispatch<React.SetStateAction<EnvironmentState>>;
  activeEnv: Environment | null;
  connectionId: string | null;
  setConnectionId: React.Dispatch<React.SetStateAction<string | null>>;
}

const EnvironmentContext = createContext<EnvironmentContextType | undefined>(undefined);

interface EnvironmentProviderProps {
  children: React.ReactNode;
}

export function EnvironmentProvider({ children }: EnvironmentProviderProps) {
  // 环境状态管理
  const [environmentState, setEnvironmentState] = useState<EnvironmentState>({
    environments: [],
    activeEnvId: null,
  });

  // 连接状态管理
  const [connectionId, setConnectionId] = useState<string | null>(null);

  // 使用环境持久化Hook
  useEnvironmentPersistence({
    environmentState,
    setEnvironmentState,
  });

  // 获取当前活动环境
  const activeEnv = environmentState.environments.find(env => env.id === environmentState.activeEnvId) || null;

  const value: EnvironmentContextType = {
    environmentState,
    setEnvironmentState,
    activeEnv,
    connectionId,
    setConnectionId,
  };

  return (
    <EnvironmentContext.Provider value={value}>
      {children}
    </EnvironmentContext.Provider>
  );
}

export function useEnvironment() {
  const context = useContext(EnvironmentContext);
  if (context === undefined) {
    throw new Error('useEnvironment must be used within an EnvironmentProvider');
  }
  return context;
}
