"use client"

import React, { useState, useEffect, useCallback, useRef } from 'react';

// lucide图标
import { Unplug, Loader2, Wifi } from "lucide-react"

// shadcn/ui组件
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroupContent
} from "@/components/ui/sidebar"

// 工具函数
import { connectTcpServer, disconnectTcpServer } from "@/components/utils/env-utils"

// hooks
import { useToast } from "@/hooks/use-toast"

// 类型定义
import type { Environment } from "@/types/env"

type ConnState = "disconnected" | "connecting" | "connected" | "disconnecting";

interface ConnSwitcherProps {
  activeEnv: Environment | null;
  onConnectionChange?: (connectionId: string | null) => void;
}

// 连接状态切换器
export function ConnSwitcher({ activeEnv, onConnectionChange }: ConnSwitcherProps) {
  const [connState, setConnState] = useState<ConnState>("disconnected");
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [connectingDots, setConnectingDots] = useState("");
  const { toast } = useToast();

  // 用于跟踪上一次的环境ID，避免在连接状态变化时误触发断开
  const prevActiveEnvIdRef = useRef<string | null>(null);

  // 连接中状态的动画效果
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (connState === "connecting" || connState === "disconnecting") {
      interval = setInterval(() => {
        setConnectingDots(prev => {
          if (prev === "") return ".";
          if (prev === ".") return "..";
          if (prev === "..") return "...";
          return "";
        });
      }, 500); // 每500ms变化一次
    } else {
      setConnectingDots("");
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [connState]);

  // 连接到服务器
  const handleConnect = async () => {
    if (!activeEnv) {
      toast({
        title: "连接失败",
        description: "请先选择一个环境",
        variant: "destructive",
      });
      return;
    }

    setConnState("connecting");

    try {
      const connId = await connectTcpServer(activeEnv.addr, Number(activeEnv.port));
      setConnectionId(connId);
      setConnState("connected");
      onConnectionChange?.(connId);

      toast({
        title: "连接成功",
        description: `已连接到 ${activeEnv.name} (${activeEnv.addr}:${activeEnv.port})`,
        duration: 2000,
      });
    } catch (error) {
      setConnState("disconnected");
      toast({
        title: `连接环境[${activeEnv.name}]失败`,
        description: `${error}`,
        variant: "destructive",
      });
    }
  };

  // 断开连接
  const handleDisconnect = useCallback(async () => {
    if (!connectionId) return;

    setConnState("disconnecting");

    try {
      await disconnectTcpServer(connectionId);
      setConnectionId(null);
      setConnState("disconnected");
      onConnectionChange?.(null);

      toast({
        title: "已断开连接",
        description: activeEnv ? `已断开与 ${activeEnv.name} 的连接` : "连接已断开",
        duration: 2000,
      });
    } catch (error) {
      setConnState("connected"); // 回滚状态
      toast({
        title: "断开连接失败",
        description: `${error}`,
        variant: "destructive",
      });
    }
  }, [connectionId, activeEnv, onConnectionChange, toast]);

  // 当活动环境改变时，断开当前连接
  useEffect(() => {
    const currentEnvId = activeEnv?.id || null;

    // 只有当环境ID真正改变时才断开连接
    if (prevActiveEnvIdRef.current !== null &&
        prevActiveEnvIdRef.current !== currentEnvId &&
        connectionId &&
        connState === "connected") {
      handleDisconnect();
    }

    // 更新上一次的环境ID
    prevActiveEnvIdRef.current = currentEnvId;
  }, [activeEnv?.id, connectionId, connState, handleDisconnect]);

  // 处理点击事件
  const handleClick = () => {
    if (connState === "disconnected") {
      handleConnect();
    } else if (connState === "connected") {
      handleDisconnect();
    }
    // 连接中和断开中状态不响应点击
  };

  // 获取按钮文本和图标
  const getButtonContent = () => {
    switch (connState) {
      case "disconnected":
        return {
          icon: <Unplug />,
          text: activeEnv ? `点此连接到 ${activeEnv.name}` : "尚未连接"
        };
      case "connecting":
        return {
          icon: <Loader2 className="animate-spin" />,
          text: `连接中${connectingDots}`
        };
      case "connected":
        return {
          icon: <Wifi className={isHovered ? "animate-pulse" : ""} />,
          text: isHovered
            ? "点此关闭当前连接"
            : (activeEnv ? `已连接 ${activeEnv.name}` : "已连接")
        };
      case "disconnecting":
        return {
          icon: <Loader2 className="animate-spin" />,
          text: `断开中${connectingDots}`
        };
    }
  };

  const { icon, text } = getButtonContent();
  const isDisabled = connState === "connecting" || connState === "disconnecting";

  // 获取按钮样式
  const getButtonClassName = () => {
    const baseClasses = "min-w-8 duration-200 ease-linear disabled:opacity-50 disabled:cursor-not-allowed";

    switch (connState) {
      case "disconnected":
        return `${baseClasses} bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground`;
      case "connecting":
        return `${baseClasses} bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground animate-pulse`;
      case "connected":
        return isHovered
          ? `${baseClasses} bg-red-400 text-white hover:bg-red-400 active:bg-red-400`
          : `${baseClasses} bg-teal-600 text-white hover:bg-red-400 hover:text-white active:bg-red-400`;
      case "disconnecting":
        return `${baseClasses} bg-orange-500 text-white hover:bg-orange-600 active:bg-orange-600 animate-pulse`;
      default:
        return baseClasses;
    }
  };

  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip={text}
              className={getButtonClassName()}
              onClick={handleClick}
              disabled={isDisabled}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {icon}
              <span>{text}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
