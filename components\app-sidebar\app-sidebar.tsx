"use client"

import React, { useState } from 'react';

// shadcn/ui组件
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarTrigger,
} from "@/components/ui/sidebar"

// 应用数据
import { route_datas } from "@/app/app-datas";

// dev
import { NavBoards } from "@/components/app-sidebar/nav-boards"
import { NavApps } from "@/components/app-sidebar/nav-apps"
import { ConnSwitcher } from '@/components/app-sidebar/nav-conn';
import { EnvSwitcher } from "@/components/app-sidebar/env-switcher"
import { ModeToggle } from "@/components/theme-switcher/theme-switcher"

// hooks
import { useEnvironment } from "@/components/contexts/EnvironmentContext"

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [activeItem, setActiveItem] = useState("");

  // 使用环境上下文
  const { environmentState, setEnvironmentState, activeEnv, connectionId, setConnectionId } = useEnvironment();

  return (
    <Sidebar collapsible="icon" variant="sidebar" {...props}>
      <SidebarHeader>
        <EnvSwitcher
          environmentState={environmentState}
          setEnvironmentState={setEnvironmentState}
        />
      </SidebarHeader>
      <SidebarContent>
        <ConnSwitcher
          activeEnv={activeEnv}
          onConnectionChange={setConnectionId}
        />
        <NavBoards
          conn_types={route_datas.conn_types}
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          connectionId={connectionId}
        />
        <NavApps apps={route_datas.apps} activeItem={activeItem} setActiveItem={setActiveItem}/>
      </SidebarContent>
      <SidebarFooter>
        <SidebarTrigger />
        <ModeToggle />
      </SidebarFooter>
    </Sidebar>
  )
}
