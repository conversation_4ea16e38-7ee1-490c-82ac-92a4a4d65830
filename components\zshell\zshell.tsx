"use client";

import React, { useState } from 'react';

import { cn } from "@/lib/utils"

import {
  Card,
  CardContent,
} from "@/components/ui/card"

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable"
import { Button } from "@/components/ui/button"

import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command"

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// lucide图标
import {
  AudioWaveform,
  Check,
  ChevronsUpDown,
  Notebook,
  Cpu
} from "lucide-react"

import { Terminal } from "@/components/zshell/terminal"
import { ScriptPanel } from "@/components/zshell/script-panel"
import { DataSamplingPanel } from "@/components/zshell/data-sampling-panel"
import { RegisterPanel } from "@/components/zshell/register-panel"
import { PanelMode, usePanelMode, PanelModeProvider } from "@/components/zshell/contexts/PanelModeContext"

type ZShellProps = React.ComponentProps<typeof Card> & {
  connectionId?: string | null;
  boardId?: string;
  activeEnv?: {
    name: string;
    addr: string;
    port: number;
  } | null;
}

const modes = [
  {
    value: "脚本" as PanelMode,
    icon: Notebook
  },
  {
    value: "采数" as PanelMode,
    icon: AudioWaveform,
  },
  {
    value: "寄存器" as PanelMode,
    icon: Cpu,
  },
];

function ToolsBar({ tools }: { tools: React.ReactNode }) {
  return (
    <div data-tauri-drag-region className="bg-background border-b border-border flex-shrink-0 relative h-9">
      {tools}
    </div>
  )
}

function ModeSwitcher() {
  const [open, setOpen] = useState(false)
  const { mode, setMode } = usePanelMode();

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild className='m-1'>
        <Button
          variant="ghost"
          role="combobox"
          aria-expanded={open}
          className="h-7 w-[100px] justify-between text-xs text-muted-foreground hover:border-r-0 hover:text-foreground transition-colors"
        >
          {mode}模式
          <ChevronsUpDown className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandGroup>
              {modes.map((item) => (
                <CommandItem
                  key={item.value}
                  value={item.value}
                  onSelect={(currentValue) => {
                    setMode(currentValue as PanelMode)
                    setOpen(false)
                  }}
                >
                  {item.icon && <item.icon />}
                  <span>{item.value}模式</span>
                  <Check
                    className={cn(
                      "ml-auto",
                      mode === item.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

function ShellPanel({ className, connectionId, boardId, activeEnv, ...props }: ZShellProps) {
  const { mode } = usePanelMode();

  // 根据当前模式渲染对应的面板
  const renderActivePanel = () => {
    switch (mode) {
      case "脚本":
        return <ScriptPanel connectionId={connectionId} boardId={boardId} />;
      case "采数":
        return <DataSamplingPanel />;
      case "寄存器":
        return <RegisterPanel />;
      default:
        return <ScriptPanel connectionId={connectionId} boardId={boardId} />;
    }
  };

  return (
    <Card className={cn("border-none h-full flex flex-col " + className)} {...props}>
      <CardContent className="w-full h-full flex-1 p-0">
        <ResizablePanelGroup
          direction="horizontal"
          className="w-full h-full flex-1 p-0"
        >
          {/* 输出面板 */}
          <ResizablePanel defaultSize={60}>
            {/* 工具栏 */}
            <ToolsBar
              tools={<></>}
            />
            {/* 输出终端 */}
            <Terminal connectionId={connectionId} boardId={boardId} activeEnv={activeEnv} />
          </ResizablePanel>

          {/* 分割线 */}
          <ResizableHandle withHandle />

          {/* 输入面板 */}
          <ResizablePanel defaultSize={40}>
            <div className="h-full flex flex-col">
              {/* 工具栏 */}
              <ToolsBar
                tools={<ModeSwitcher />}
              />
              {/* 当前活动面板 */}
              <div className="flex-1">
                {renderActivePanel()}
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </CardContent>
    </Card>
  )
}

export function ZShell({ className, connectionId, boardId, activeEnv, ...props }: ZShellProps) {
  return (
    <PanelModeProvider>
      <ShellPanel className={className} connectionId={connectionId} boardId={boardId} activeEnv={activeEnv} {...props} />
    </PanelModeProvider>
  )
}

// 为了兼容React.lazy，添加默认导出
const ShellExports = { ZShell };
export default ShellExports;
